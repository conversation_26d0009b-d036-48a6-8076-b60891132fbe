"use client";

import React from "react";
import { Select, Space, Typography, Al<PERSON>, Row, Col, Card, Tag, Statistic } from "antd";
import { InfoCircleOutlined, ThunderboltOutlined } from "@ant-design/icons";
import { FrequencyFilter, FileInfo } from "@/types/eeg";

const { Text } = Typography;

interface FrequencySectionProps {
  parameters: FrequencyFilter;
  onUpdate: (parameters: FrequencyFilter) => void;
  errors: string[];
  isExpanded: boolean;
  onToggle: () => void;
  status: "valid" | "error" | "success";
  fileInfo: FileInfo;
}

const FREQUENCY_OPTIONS = {
  low_cutoff: [1, 4, 8, 14, 30, 50, 70, 80, 120, 200, 250],
  high_cutoff: [4, 8, 14, 30, 50, 70, 80, 120, 160, 200, 300, 330, 600, 660],
};

const PRESET_BANDS = [
  { name: "Ripple", low: 80, high: 250, description: "Standard ripple band" },
  { name: "Fast Ripple", low: 250, high: 500, description: "Fast ripple oscillations" },
  { name: "Wide Band", low: 80, high: 500, description: "Combined ripple and fast ripple" },
  { name: "Custom", low: null, high: null, description: "Custom frequency range" },
];

const FrequencySection: React.FC<FrequencySectionProps> = ({ 
  parameters, 
  onUpdate, 
  errors, 
  fileInfo 
}) => {
  const updateLowCutoff = (value: number) => {
    onUpdate({
      ...parameters,
      low_cutoff: value,
    });
  };

  const updateHighCutoff = (value: number) => {
    onUpdate({
      ...parameters,
      high_cutoff: value,
    });
  };

  const applyPreset = (low: number, high: number) => {
    onUpdate({
      low_cutoff: low,
      high_cutoff: high,
    });
  };

  const samplingRate = fileInfo.sampling_rate;
  const nyquistFreq = samplingRate / 2;
  const maxUsableFreq = samplingRate / 3;

  // Filter high cutoff options based on sampling rate
  const validHighCutoffOptions = FREQUENCY_OPTIONS.high_cutoff.filter((freq) => freq <= nyquistFreq);
  const validLowCutoffOptions = FREQUENCY_OPTIONS.low_cutoff.filter((freq) => freq < parameters.high_cutoff);

  const getCurrentPreset = () => {
    return PRESET_BANDS.find(
      band => band.low === parameters.low_cutoff && band.high === parameters.high_cutoff
    )?.name || "Custom";
  };

  return (
    <Space direction="vertical" size="large" className="w-full">
      {/* Error Display */}
      {errors.length > 0 && (
        <Alert
          message="Validation Errors"
          description={
            <ul className="list-disc list-inside">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          }
          type="error"
          showIcon
        />
      )}

      {/* Sampling Rate Information */}
      <Row gutter={16}>
        <Col span={8}>
          <Card size="small" className="bg-blue-50">
            <Statistic
              title="Sampling Rate"
              value={samplingRate}
              suffix="Hz"
              prefix={<ThunderboltOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small" className="bg-green-50">
            <Statistic
              title="Nyquist Frequency"
              value={nyquistFreq}
              suffix="Hz"
              valueStyle={{ color: nyquistFreq < parameters.high_cutoff ? '#ff4d4f' : '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small" className="bg-yellow-50">
            <Statistic
              title="Max Usable Freq"
              value={Math.round(maxUsableFreq)}
              suffix="Hz"
            />
          </Card>
        </Col>
      </Row>

      {/* Preset Bands */}
      <Card size="small">
        <Space direction="vertical" size="small" className="w-full">
          <Text strong>Preset Frequency Bands</Text>
          <div className="flex flex-wrap gap-2">
            {PRESET_BANDS.filter(band => band.low !== null).map((band) => {
              const isValid = band.high! <= nyquistFreq;
              const isActive = band.low === parameters.low_cutoff && band.high === parameters.high_cutoff;
              
              return (
                <Tag
                  key={band.name}
                  color={isActive ? "black" : isValid ? "default" : "red"}
                  className={`cursor-pointer px-3 py-1 ${isActive ? "border-2 border-black" : ""}`}
                  onClick={() => isValid && applyPreset(band.low!, band.high!)}
                >
                  <Space size="small">
                    <Text strong={isActive}>{band.name}</Text>
                    <Text type="secondary" className="text-xs">
                      ({band.low}-{band.high} Hz)
                    </Text>
                  </Space>
                </Tag>
              );
            })}
          </div>
        </Space>
      </Card>

      {/* Frequency Selectors */}
      <Row gutter={16}>
        <Col span={12}>
          <Card size="small">
            <Space direction="vertical" className="w-full">
              <div className="flex items-center justify-between">
                <Text strong>Low Cutoff Frequency</Text>
                <Tag color="blue">{parameters.low_cutoff} Hz</Tag>
              </div>
              <Select
                value={parameters.low_cutoff}
                onChange={updateLowCutoff}
                options={validLowCutoffOptions.map(freq => ({
                  label: `${freq} Hz`,
                  value: freq,
                }))}
                className="w-full"
                size="large"
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
              <Text type="secondary" className="text-xs">
                High-pass filter to remove low-frequency components
              </Text>
            </Space>
          </Card>
        </Col>

        <Col span={12}>
          <Card size="small">
            <Space direction="vertical" className="w-full">
              <div className="flex items-center justify-between">
                <Text strong>High Cutoff Frequency</Text>
                <Tag color="blue">{parameters.high_cutoff} Hz</Tag>
              </div>
              <Select
                value={parameters.high_cutoff}
                onChange={updateHighCutoff}
                options={validHighCutoffOptions.map(freq => ({
                  label: `${freq} Hz`,
                  value: freq,
                  disabled: freq <= parameters.low_cutoff,
                }))}
                className="w-full"
                size="large"
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
              <Text type="secondary" className="text-xs">
                Low-pass filter to remove high-frequency components
              </Text>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Bandwidth Display */}
      <Card size="small" className="bg-gray-50">
        <Row align="middle">
          <Col span={12}>
            <Space>
              <Text strong>Current Bandwidth:</Text>
              <Tag color="black" className="text-base">
                {parameters.low_cutoff} - {parameters.high_cutoff} Hz
              </Tag>
            </Space>
          </Col>
          <Col span={12}>
            <Space>
              <Text strong>Bandwidth Width:</Text>
              <Tag color="green" className="text-base">
                {parameters.high_cutoff - parameters.low_cutoff} Hz
              </Tag>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Information Box */}
      <Alert
        message="About Frequency Filtering"
        description={
          <Space direction="vertical" size="small">
            <Text>Frequency filtering isolates the signal components relevant to HFO detection:</Text>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>
                <Text strong>Ripples (80-250 Hz)</Text>: Most common HFO type in epilepsy
              </li>
              <li>
                <Text strong>Fast Ripples (250-500 Hz)</Text>: Associated with epileptogenic zones
              </li>
              <li>
                <Text strong>Nyquist Limit</Text>: High cutoff must be ≤ {nyquistFreq} Hz (sampling rate/2)
              </li>
              <li>
                <Text strong>Recommended</Text>: High cutoff ≤ {Math.round(maxUsableFreq)} Hz (sampling rate/3)
              </li>
            </ul>
            {parameters.high_cutoff > maxUsableFreq && (
              <Alert
                message="Performance Warning"
                description={`High cutoff (${parameters.high_cutoff} Hz) exceeds recommended limit (${Math.round(maxUsableFreq)} Hz). This may lead to aliasing artifacts.`}
                type="warning"
                showIcon
                className="mt-2"
              />
            )}
          </Space>
        }
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
      />
    </Space>
  );
};

export default FrequencySection;