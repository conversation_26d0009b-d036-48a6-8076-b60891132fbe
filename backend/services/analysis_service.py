import sys
import numpy as np
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

# Add hfo_engine to path
sys.path.append('./core/hfo_engine')

from pyedfreader import edfread
from browse_files import browse_edf
from hfo_analysis import run_hfo_algorithm

from models.parameters import AnalysisParameters
from core.validators import EDFValidator, ParameterValidator, SignalValidator
from core.exceptions.validation_exceptions import ValidationError

class EEGStreamProcessor:
    """Processes EEG files in chunks for real-time streaming"""
    
    def __init__(self, file_path: str, parameters: AnalysisParameters):
        self.file_path = file_path
        self.parameters = parameters
        self.chunk_duration = 10  # Process in 10-second chunks
        self.header = None
        self.record_data = None
        self.total_hfos = 0
        self.all_results = []
        self.hfo_results = None  # Store HFO detection results
        
        # Initialize validators
        self.edf_validator = EDFValidator()
        self.param_validator = ParameterValidator()
        self.signal_validator = SignalValidator()
        
    async def initialize(self):
        """Load EDF file header and prepare for processing with validation"""
        try:
            # Validate file first
            is_valid, errors = self.edf_validator.validate_file(self.file_path)
            if not is_valid:
                raise ValidationError(
                    message="EDF file validation failed during initialization",
                    errors=errors
                )
            
            # Load header information
            self.header = browse_edf(self.file_path)
            
            # Validate header
            is_valid, errors = self.edf_validator.validate_header(self.header)
            if not is_valid:
                raise ValidationError(
                    message="EDF header validation failed",
                    errors=errors
                )
            
            # Load the full data (we'll process it in chunks)
            self.header, self.record_data = edfread(self.file_path)
            
            # Validate signal quality for first chunk
            srate = self._get_sampling_rate()
            first_chunk_samples = min(int(10 * srate), self.record_data.shape[1])
            first_chunk = self.record_data[:, :first_chunk_samples]
            
            is_valid, errors = self.signal_validator.validate_signal_quality(
                first_chunk, srate, self.header['label']
            )
            if not is_valid:
                logger.warning(f"Signal quality issues detected: {errors}")
            
            # Process channel labels
            self._process_header_labels()
            
            # Run HFO detection on the entire file
            self._run_hfo_detection()
            
            return True
        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Error initializing processor: {e}")
            raise
    
    def _process_header_labels(self):
        """Clean and process header labels"""
        import re
        for idx, label in enumerate(self.header['label']):
            channel_id = re.search(r'\d+', label)
            channel_name = re.sub(r'^(POL )|(P )|(\d+)|( )', '', label)
            if channel_id and channel_name:
                self.header['label'][idx] = f"{channel_name}{channel_id.group()}"
            elif channel_name:
                self.header['label'][idx] = channel_name
    
    def _run_hfo_detection(self):
        """Run the HFO detection algorithm on the entire file"""
        try:
            # Prepare EEG dictionary for the algorithm
            EEG = {
                'data': self.record_data,
                'nbchan': self.header['ns'],  # number of channels
                'srate': self._get_sampling_rate(),
                'chanlocs': self.header['label'],
                'records': self.header.get('records', 1),
                'duration': self.header.get('duration', 1)
            }
            
            # Map frontend parameters to algorithm parameters
            threshold_option1 = self.parameters.thresholds.amplitude1
            threshold_option2 = self.parameters.thresholds.amplitude2
            threshold_option3 = self.parameters.thresholds.peaks1
            threshold_option4 = self.parameters.thresholds.peaks2
            threshold_option5 = self.parameters.thresholds.duration
            threshold_option6 = self.parameters.thresholds.temporal_sync
            threshold_option7 = self.parameters.thresholds.spatial_sync
            
            # Determine time segment
            analysis_start = 0
            analysis_end = -1  # -1 means entire file
            
            if self.parameters.time_segment.mode == "start_end_times":
                # TODO: Parse dates and convert to seconds
                pass
            elif self.parameters.time_segment.mode == "start_time_duration":
                # TODO: Parse start time and duration
                if hasattr(self.parameters.time_segment, 'duration_seconds'):
                    analysis_end = self.parameters.time_segment.duration_seconds
            
            # Get montage settings
            montage = self.parameters.montage.type
            if montage == "bipolar":
                montage = "Bipolar montage"
            elif montage == "average":
                montage = "Average montage"
            elif montage == "referential":
                montage = "Referential montage"
            
            user_ref = ""
            if self.parameters.montage.type == "referential":
                user_ref = self.parameters.montage.reference_channel or ""
            
            # Get frequency settings
            locutoff = self.parameters.frequency.low_cutoff
            hicutoff = self.parameters.frequency.high_cutoff
            
            # Run the HFO detection algorithm
            logger.info(f"Running HFO detection with parameters: {locutoff}-{hicutoff}Hz, montage: {montage}")
            self.hfo_results = run_hfo_algorithm(
                EEG,
                self.file_path,
                analysis_start,
                analysis_end,
                montage,
                user_ref,
                locutoff,
                hicutoff,
                None,  # gui_output callback
                threshold_option1,
                threshold_option2,
                threshold_option3,
                threshold_option4,
                threshold_option5,
                threshold_option6,
                threshold_option7
            )
            
            if self.hfo_results and self.hfo_results.get('success'):
                # Extract HFO count
                if 'final_start_ind' in self.hfo_results:
                    self.total_hfos = len(self.hfo_results['final_start_ind'])
                logger.info(f"HFO detection completed successfully. Found {self.total_hfos} HFOs")
            else:
                logger.warning("HFO detection did not complete successfully")
                self.hfo_results = None
                
        except Exception as e:
            logger.error(f"Error running HFO detection: {e}")
            import traceback
            logger.error(traceback.format_exc())
            self.hfo_results = None
    
    async def process_preview(self) -> Dict[str, Any]:
        """Process first 30 seconds for immediate preview"""
        try:
            # Calculate sample indices for first 30 seconds
            # Handle frequency as NumPy array, list, or scalar
            srate = self._get_sampling_rate()
            
            # Count HFOs in first 30 seconds if available
            preview_hfo_count = 0
            if self.hfo_results and 'final_start_ind' in self.hfo_results:
                # Count HFOs in first 30 seconds
                preview_samples = int(30 * srate)
                for start_idx in self.hfo_results['final_start_ind']:
                    if start_idx < preview_samples:
                        preview_hfo_count += 1
            
            return {
                "preview_duration": 30,
                "channels": self.header['label'],
                "sampling_rate": srate,
                "quick_hfo_count": preview_hfo_count,
                "message": "Preview ready, full analysis continuing..."
            }
            
        except Exception as e:
            logger.error(f"Error processing preview: {e}")
            return {"error": str(e)}
    
    def get_total_chunks(self) -> int:
        """Calculate total number of chunks to process"""
        # Handle frequency as NumPy array, list, or scalar
        srate = self._get_sampling_rate()
        total_samples = self.record_data.shape[1]
        chunk_samples = int(self.chunk_duration * srate)
        return int(np.ceil(total_samples / chunk_samples))
    
    async def process_chunk(self, chunk_num: int) -> Dict[str, Any]:
        """Process a specific chunk of the EEG data"""
        try:
            # Handle frequency as NumPy array, list, or scalar
            srate = self._get_sampling_rate()
            chunk_samples = int(self.chunk_duration * srate)
            
            # Calculate chunk boundaries
            start_sample = chunk_num * chunk_samples
            end_sample = min((chunk_num + 1) * chunk_samples, self.record_data.shape[1])
            
            # Extract chunk data
            chunk_data = self.record_data[:, start_sample:end_sample]
            
            # Calculate time range
            start_time = start_sample / srate
            end_time = end_sample / srate
            
            # Extract HFOs for this chunk
            chunk_hfos = self._extract_hfos_for_chunk(start_sample, end_sample, srate)
            
            # Prepare channel data for visualization (downsample if needed)
            channel_data = self._prepare_channel_data(chunk_data)
            
            return {
                "time_range": [start_time, end_time],
                "hfo_events": chunk_hfos,
                "channel_data": channel_data,
                "chunk_number": chunk_num
            }
            
        except Exception as e:
            logger.error(f"Error processing chunk {chunk_num}: {e}")
            return {
                "time_range": [0, 0],
                "hfo_events": [],
                "channel_data": {},
                "error": str(e)
            }
    
    def _extract_hfos_for_chunk(self, start_sample: int, end_sample: int, srate: float) -> List[Dict[str, Any]]:
        """Extract HFOs that fall within the given sample range"""
        chunk_hfos = []
        
        if not self.hfo_results or 'final_start_ind' not in self.hfo_results:
            return chunk_hfos
        
        try:
            # Get HFO arrays from results
            start_indices = self.hfo_results.get('final_start_ind', [])
            end_indices = self.hfo_results.get('final_end_ind', [])
            channel_labels = self.hfo_results.get('channel_labels', [])
            
            # Get optional HFO characteristics if available
            durations = self.hfo_results.get('duration', [])
            peak_freqs = self.hfo_results.get('peak_freq', [])
            hfo_powers = self.hfo_results.get('hfo_power', [])
            amplitudes = self.hfo_results.get('my_amp', [])
            
            # Iterate through HFOs and find ones in this chunk
            for i in range(len(start_indices)):
                hfo_start = start_indices[i]
                hfo_end = end_indices[i] if i < len(end_indices) else hfo_start
                
                # Check if HFO is within this chunk
                if hfo_start >= start_sample and hfo_start < end_sample:
                    # Convert to time relative to chunk start
                    hfo_time_start = (hfo_start - start_sample) / srate
                    hfo_time_end = (hfo_end - start_sample) / srate
                    
                    hfo_event = {
                        "start_time": hfo_time_start,
                        "end_time": hfo_time_end,
                        "channel": channel_labels[i] if i < len(channel_labels) else f"Channel {i}",
                        "sample_start": hfo_start - start_sample,
                        "sample_end": hfo_end - start_sample
                    }
                    
                    # Add optional characteristics if available
                    if i < len(durations):
                        hfo_event["duration_ms"] = durations[i]
                    if i < len(peak_freqs):
                        hfo_event["peak_frequency"] = peak_freqs[i]
                    if i < len(hfo_powers):
                        hfo_event["power"] = hfo_powers[i]
                    if i < len(amplitudes):
                        hfo_event["amplitude"] = amplitudes[i]
                    
                    chunk_hfos.append(hfo_event)
            
            logger.info(f"Found {len(chunk_hfos)} HFOs in chunk")
            
        except Exception as e:
            logger.error(f"Error extracting HFOs for chunk: {e}")
        
        return chunk_hfos
    
    def _get_sampling_rate(self) -> float:
        """Extract sampling rate from header, handling various data types"""
        freq = self.header.get('frequency')
        
        if freq is None:
            raise ValueError("No frequency information found in EDF header")
        
        # Handle NumPy array
        if isinstance(freq, np.ndarray):
            if len(freq) == 0:
                raise ValueError("Empty frequency array in EDF header")
            elif len(freq) == 1:
                return float(freq[0])
            elif np.all(freq == freq[0]):
                # All channels have same sampling rate
                return float(freq[0])
            else:
                # Different sampling rates across channels - use mean and warn
                logger.warning(f"Different sampling rates across channels: {freq}")
                return float(np.mean(freq))
        
        # Handle list
        elif isinstance(freq, list):
            if len(freq) == 0:
                raise ValueError("Empty frequency list in EDF header")
            elif len(freq) == 1:
                return float(freq[0])
            elif all(f == freq[0] for f in freq):
                return float(freq[0])
            else:
                # Different sampling rates - use mean and warn
                logger.warning(f"Different sampling rates across channels: {freq}")
                return float(sum(freq) / len(freq))
        
        # Handle scalar
        else:
            return float(freq)
    
    def _prepare_channel_data(self, chunk_data: np.ndarray) -> Dict[str, List[float]]:
        """Prepare channel data for visualization"""
        channel_data = {}
        
        # Downsample if necessary (max 1000 points per channel for web display)
        max_points = 1000
        if chunk_data.shape[1] > max_points:
            downsample_factor = chunk_data.shape[1] // max_points
            downsampled = chunk_data[:, ::downsample_factor]
        else:
            downsampled = chunk_data
        
        # Convert to dictionary
        
        # Get selected leads from channel_selection
        selected_leads = []
        if hasattr(self.parameters, 'channel_selection') and self.parameters.channel_selection:
            selected_leads = self.parameters.channel_selection.selected_leads
        
        for i, label in enumerate(self.header['label']):
            if selected_leads:
                # Check if this channel belongs to a selected lead group
                import re
                match = re.match(r'^(?:POL |P )?(\w+?)(\d+)$', label)
                if match:
                    lead_name = match.group(1)
                    if lead_name in selected_leads:
                        channel_data[label] = downsampled[i].tolist()
                elif label in selected_leads:
                    # Direct match for non-numbered channels
                    channel_data[label] = downsampled[i].tolist()
            else:
                # If no specific channels selected, include all
                channel_data[label] = downsampled[i].tolist()
        return channel_data
    
    def get_total_hfos(self) -> int:
        """Get total number of HFOs detected"""
        return self.total_hfos
    
    def get_summary(self) -> Dict[str, Any]:
        """Get analysis summary"""
        # Determine number of channels analyzed
        num_channels = len(self.header['label'])
        if hasattr(self.parameters, 'channel_selection') and self.parameters.channel_selection:
            selected_leads = self.parameters.channel_selection.selected_leads
            if selected_leads:
                # Count channels belonging to selected leads
                import re
                selected_count = 0
                for label in self.header['label']:
                    match = re.match(r'^(?:POL |P )?(\w+?)(\d+)$', label)
                    if match and match.group(1) in selected_leads:
                        selected_count += 1
                    elif label in selected_leads:
                        selected_count += 1
                num_channels = selected_count
        
        return {
            "total_hfos": self.total_hfos,
            "total_chunks_processed": len(self.all_results),
            "channels_analyzed": num_channels,
            "frequency_range": f"{self.parameters.frequency.low_cutoff}-{self.parameters.frequency.high_cutoff} Hz",
            "montage": self.parameters.montage.type
        }

class AnalysisService:
    """Service for managing EEG analysis"""
    
    async def create_processor(self, file_path: str, parameters: AnalysisParameters) -> EEGStreamProcessor:
        """Create and initialize an EEG processor"""
        processor = EEGStreamProcessor(file_path, parameters)
        await processor.initialize()
        return processor