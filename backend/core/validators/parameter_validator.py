"""
Unified parameter validator that delegates to domain-specific validators.
This is a facade that maintains the original interface while using the new modular validators.
"""

from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
from .base_validator import BaseValidator
from .domain import ThresholdValidator, FrequencyValidator, TimeValidator, MontageValidator


class ParameterValidator(BaseValidator):
    """Unified validator for all HFO detection parameters"""
    
    def __init__(self):
        super().__init__()
        self.threshold_validator = ThresholdValidator()
        self.frequency_validator = FrequencyValidator()
        self.time_validator = TimeValidator()
        self.montage_validator = MontageValidator()
    
    def validate_thresholds(self, thresholds: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate HFO detection threshold parameters"""
        return self.threshold_validator.validate(thresholds)
    
    def validate_frequency_filter(self, frequency: Dict[str, Any], 
                                 sampling_rate: Optional[float] = None) -> <PERSON><PERSON>[bool, List[str]]:
        """Validate frequency filter parameters"""
        return self.frequency_validator.validate(frequency, sampling_rate)
    
    def validate_montage(self, montage: Dict[str, Any],
                        available_channels: Optional[List[str]] = None) -> Tuple[bool, List[str]]:
        """Validate montage configuration"""
        return self.montage_validator.validate(montage, available_channels)
    
    def validate_time_segment(self, time_segment: Dict[str, Any],
                            file_duration: Optional[float] = None,
                            file_start_time: Optional[datetime] = None) -> Tuple[bool, List[str]]:
        """Validate time segment parameters"""
        return self.time_validator.validate(time_segment, file_duration, file_start_time)
    
    def validate_channel_selection(self, channel_selection: Dict[str, Any],
                                 available_leads: Optional[List[str]] = None) -> Tuple[bool, List[str]]:
        """
        Validate channel selection parameters
        
        Args:
            channel_selection: Dictionary with selected_leads and contact_specs
            available_leads: List of available lead groups (if known)
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        if 'selected_leads' not in channel_selection:
            self.add_error("Missing required field: selected_leads")
            return False, self.errors
        
        selected_leads = channel_selection.get('selected_leads', [])
        if not isinstance(selected_leads, list):
            self.add_error(f"Invalid type for selected_leads: expected list, got {type(selected_leads).__name__}")
            return False, self.errors
        
        if not selected_leads:
            self.add_error("At least one lead must be selected")
        
        # Validate contact specifications if provided
        contact_specs = channel_selection.get('contact_specs', {})
        if contact_specs:
            self._validate_contact_specs(contact_specs, selected_leads)
        
        # Validate against available leads if provided
        if available_leads and selected_leads:
            invalid_leads = [lead for lead in selected_leads if lead not in available_leads]
            if invalid_leads:
                self.add_error(f"Invalid lead selections: {', '.join(invalid_leads)}")
        
        return not self.has_errors(), self.errors
    
    def _validate_contact_specs(self, contact_specs: Dict[str, str], selected_leads: List[str]) -> None:
        """Validate contact specifications format"""
        import re
        
        for lead, spec in contact_specs.items():
            if lead not in selected_leads:
                self.add_warning(f"Contact specification for unselected lead: {lead}")
                continue
            
            # Validate specification format
            # Valid formats: "1-5", "1,3,5", "1-3,5,7-9"
            if not re.match(r'^(\d+(-\d+)?)(,\d+(-\d+)?)*$', spec.replace(' ', '')):
                self.add_error(
                    f"Invalid contact specification for {lead}: '{spec}'. "
                    "Expected format: '1-5' or '1,3,5' or '1-3,5,7-9'"
                )
    
    def validate_all_parameters(self, parameters: Dict[str, Any],
                              file_metadata: Optional[Dict[str, Any]] = None) -> Tuple[bool, Dict[str, List[str]]]:
        """
        Validate all analysis parameters at once
        
        Args:
            parameters: Complete parameter dictionary
            file_metadata: Optional metadata about the EDF file
            
        Returns:
            Tuple[bool, Dict[str, List[str]]]: (all_valid, errors_by_category)
        """
        errors_by_category = {}
        all_valid = True
        
        # Extract file metadata if provided
        sampling_rate = file_metadata.get('sampling_rate') if file_metadata else None
        file_duration = file_metadata.get('duration') if file_metadata else None
        file_start_time = file_metadata.get('start_time') if file_metadata else None
        available_channels = file_metadata.get('channels') if file_metadata else None
        available_leads = file_metadata.get('lead_groups') if file_metadata else None
        
        # Validate each category
        if 'thresholds' in parameters:
            valid, errors = self.validate_thresholds(parameters['thresholds'])
            if not valid:
                errors_by_category['thresholds'] = errors
                all_valid = False
        
        if 'frequency_filter' in parameters:
            valid, errors = self.validate_frequency_filter(
                parameters['frequency_filter'], sampling_rate
            )
            if not valid:
                errors_by_category['frequency_filter'] = errors
                all_valid = False
        
        if 'montage' in parameters:
            valid, errors = self.validate_montage(
                parameters['montage'], available_channels
            )
            if not valid:
                errors_by_category['montage'] = errors
                all_valid = False
        
        if 'time_segment' in parameters:
            valid, errors = self.validate_time_segment(
                parameters['time_segment'], file_duration, file_start_time
            )
            if not valid:
                errors_by_category['time_segment'] = errors
                all_valid = False
        
        if 'channel_selection' in parameters:
            valid, errors = self.validate_channel_selection(
                parameters['channel_selection'], available_leads
            )
            if not valid:
                errors_by_category['channel_selection'] = errors
                all_valid = False
        
        return all_valid, errors_by_category
    
    def calculate_analysis_times(self, time_segment: Dict[str, Any],
                               file_start_time: datetime,
                               file_duration: float) -> Tuple[float, float]:
        """
        Calculate actual analysis start and end times in seconds
        
        Args:
            time_segment: Validated time segment parameters
            file_start_time: Start time of the file
            file_duration: Total duration in seconds
            
        Returns:
            Tuple[float, float]: (start_seconds, end_seconds) from file start
        """
        return self.time_validator.calculate_analysis_times(
            time_segment, file_start_time, file_duration
        )